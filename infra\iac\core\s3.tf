# Document Storage Platform for ${local.api_domain_name}
# This creates S3 bucket with Lambda integration for document ingestion pipeline
# Supports multi-tenant folder structure: {user_id}/uploaded/, {user_id}/processed/, {user_id}/failed/

# Create document storage bucket with Lambda integration
module "document_storage" {
  source = "../../modules/s3_bucket"

  # Conditional bucket naming for LocalStack vs AWS
  bucket_name = var.is_localstack ? "localstack-ezychat-documents" : "${var.env}-ezychat-documents"
  force_destroy = var.is_localstack ? true : false  # Allow force destroy in LocalStack for testing

  # Lambda integration for document ingestion - triggers on CSV uploads to any user's uploaded/ folder
  lambda_integration = {
    lambda_function_arn = module.lambda_platform.lambda_function_arns["document-ingestion-lambda"]
    lambda_function_name = module.lambda_platform.lambda_function_names["document-ingestion-lambda"]
    lambda_execution_role_arn = module.lambda_platform.lambda_execution_role_arns["document-ingestion-lambda"]
    events = ["s3:ObjectCreated:*"]
    filter_prefix = ""  # No prefix filter to catch all user folders
    filter_suffix = ".csv"  # Only trigger on CSV files
  }

  # Enable lifecycle policies for cost optimization (disabled for LocalStack)
  enable_lifecycle = var.is_localstack ? false : true

  # Lifecycle rules for production environments
  lifecycle_rules = var.is_localstack ? [] : [
    {
      id     = "document_lifecycle"
      status = "Enabled"
      transition = [
        {
          days          = 30
          storage_class = "STANDARD_IA"
        },
        {
          days          = 90
          storage_class = "GLACIER"
        }
      ]
      expiration = {
        days = 2555  # 7 years retention
      }
      abort_incomplete_multipart_upload = {
        days_after_initiation = 7
      }
    }
  ]

  tags = merge(local.this.tags, {
    Name     = "${local.api_domain_name}-document-storage"
    Platform = "Document Storage"
    Domain   = local.api_domain_name
    Architecture = "Multi-Tenant"
    LocalStack = var.is_localstack ? "true" : "false"
  })
}

# Output Document Storage information
output "document_storage" {
  description = "Document Storage outputs"
  value = {
    bucket_name                = module.document_storage.bucket_name
    bucket_arn                 = module.document_storage.bucket_arn
    bucket_region              = module.document_storage.bucket_region
    bucket_domain_name         = module.document_storage.bucket_domain_name
    bucket_regional_domain_name = module.document_storage.bucket_regional_domain_name
    lambda_integration_configured = module.document_storage.lambda_integration_configured
    lifecycle_enabled = module.document_storage.lifecycle_enabled
  }
} 