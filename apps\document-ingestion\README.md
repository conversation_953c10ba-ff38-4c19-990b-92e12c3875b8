# Document Ingestion Pipeline - Clean Architecture

[![CI/CD Pipeline](https://github.com/anchorsprint/ezychat/actions/workflows/ci.yml/badge.svg)](https://github.com/anchorsprint/ezychat/actions/workflows/ci.yml)
[![Code Coverage](https://codecov.io/gh/anchorsprint/ezychat/branch/main/graph/badge.svg)](https://codecov.io/gh/anchorsprint/ezychat)
[![Python 3.12](https://img.shields.io/badge/python-3.12-blue.svg)](https://www.python.org/downloads/release/python-3120/)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Imports: isort](https://img.shields.io/badge/%20imports-isort-%231674b1?style=flat&labelColor=ef8336)](https://pycqa.github.io/isort/)

A **production-ready** multi-tenant CSV document ingestion pipeline for the EzyChat RAG-powered sales assistant, built with **Clean Architecture** principles, **dependency injection**, and comprehensive **testing infrastructure**.

## 🏗️ Architecture Overview

This project follows **Clean Architecture** patterns with proper separation of concerns and **dependency injection** using the `dependency-injector` library:

```
src/
├── domain/                  # Business entities and rules (innermost layer)
│   ├── entities.py         # Document, ProductData, EmbeddingVector
│   ├── interfaces.py       # Repository and service interfaces (ports)
│   └── services.py         # Domain services (business logic)
├── application/             # Use cases and orchestration
│   └── use_cases.py        # ProcessDocumentUseCase (application logic)
├── infrastructure/          # External dependencies (outermost layer)
│   ├── configuration.py    # Environment configuration
│   ├── logging.py          # Structured logging
│   ├── metrics.py          # Metrics collection
│   ├── repositories.py     # Supabase implementations (adapters)
│   ├── storage.py          # S3 implementations (adapters)
│   ├── services.py         # OpenAI implementations (adapters)
│   ├── processors.py       # CSV processing (adapters)
│   ├── events.py           # Event publishing (adapters)
│   └── dependency_injection.py  # DI container
└── presentation/            # Controllers and handlers
    └── lambda_handler.py   # Lambda entry point

tests/
├── unit/                   # Fast, isolated unit tests
│   ├── domain/            # Domain layer tests
│   ├── application/       # Application layer tests
│   └── infrastructure/    # Infrastructure layer tests
├── integration/           # Integration tests with real dependencies
├── fixtures/              # Test data and fixtures
└── conftest.py           # Pytest configuration and shared fixtures
```

## 🔧 Technology Stack

- **Runtime:** Python 3.12 (latest stable)
- **Architecture:** Clean Architecture with SOLID principles
- **DI Framework:** [dependency-injector](https://pypi.org/project/dependency-injector/) 4.41+
- **Vector Store:** Supabase pgvector (migrated from Qdrant)
- **Embeddings:** OpenAI text-embedding-3-small (configurable)
- **Storage:** AWS S3, Supabase PostgreSQL
- **Processing:** Pandas for CSV handling
- **Testing:** pytest with comprehensive fixtures
- **Code Quality:** black, isort, flake8, mypy, bandit
- **CI/CD:** GitHub Actions with automated testing and deployment
- **Containerization:** Docker with AWS Lambda base image

## 🚀 Key Features

### Clean Architecture Benefits
- ✅ **Separation of Concerns:** Clear boundaries between layers
- ✅ **Dependency Inversion:** Business logic doesn't depend on infrastructure
- ✅ **Testability:** Easy to unit test with mocked dependencies
- ✅ **Maintainability:** Changes in one layer don't affect others
- ✅ **Extensibility:** New features can be added without breaking existing code

### Dependency Injection Benefits
- ✅ **Loose Coupling:** Components depend on abstractions, not concretions
- ✅ **Configuration Management:** Centralized dependency configuration
- ✅ **Singleton Management:** Automatic lifecycle management
- ✅ **Testing Support:** Easy to inject mocks for testing
- ✅ **Runtime Flexibility:** Can swap implementations without code changes

### Business Features
- ✅ **Multi-tenant isolation** with user_id-based partitioning
- ✅ **Comprehensive error handling** and retry mechanisms
- ✅ **Structured logging** with CloudWatch integration
- ✅ **Configurable embeddings** (model and dimensions)
- ✅ **Batch processing** for OpenAI API efficiency
- ✅ **CSV validation and cleaning** with data quality scoring
- ✅ **S3 file lifecycle management** (uploaded → processed/failed)

## 📋 Prerequisites

- **Python 3.12+** (required for latest features and performance)
- **AWS CLI** configured with appropriate permissions
- **OpenAI API key** with embeddings access
- **Supabase project** with pgvector extension enabled
- **Git** for version control
- **Make** (optional, for convenience commands)

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

```bash
# Clone the repository
git clone https://github.com/anchorsprint/ezychat.git
cd ezychat/apps/document-ingestion

# Run automated setup script
# Linux/Mac:
./scripts/setup-dev.sh

# Windows PowerShell:
.\scripts\setup-dev.ps1
```

### Option 2: Manual Setup

1. **Create virtual environment:**
   ```bash
   python -m venv .venv

   # Activate virtual environment
   # Linux/Mac:
   source .venv/bin/activate
   # Windows:
   .venv\Scripts\activate
   ```

2. **Install dependencies:**
   ```bash
   pip install -e ".[dev]"
   ```

3. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your actual API keys and configuration
   ```

4. **Install pre-commit hooks:**
   ```bash
   pre-commit install
   ```

5. **Run tests to verify setup:**
   ```bash
   make test-unit
   # or
   pytest tests/unit -v
   ```

## 🔧 Configuration

The application uses environment variables for configuration. See `.env.example` for all available options:

### Required Configuration
```bash
OPENAI_API_KEY=sk-your-openai-api-key-here
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
```

### Optional Configuration
```bash
# Environment
ENVIRONMENT=dev
LOG_LEVEL=INFO

# OpenAI
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
OPENAI_EMBEDDING_DIMENSIONS=512

# Processing Limits
MAX_FILE_SIZE_BYTES=52428800
MAX_ROWS=100000
BATCH_SIZE=100

# Dependency Injection
DI_AUTO_WIRE=true
DI_STRICT_MODE=false
```

## 🏃‍♂️ Usage

### Lambda Deployment
The main entry point is `src/presentation/lambda_handler.py` which implements the clean architecture handler:

```python
from src.presentation.lambda_handler import lambda_handler as clean_lambda_handler

def lambda_handler(event, context):
    return clean_lambda_handler(event, context)
```

### Local Testing
```python
from src.infrastructure.dependency_injection import ApplicationContainer
from src.application.use_cases import ProcessDocumentCommand

# Initialize container
container = ApplicationContainer()
container.wire(modules=[__name__])

# Get use case
use_case = container.process_document_use_case()

# Execute
command = ProcessDocumentCommand(bucket="test-bucket", key="user123/uploaded/products.csv")
result = await use_case.execute(command)
```

## 🧪 Testing

The project includes comprehensive testing infrastructure with **unit tests**, **integration tests**, and **fixtures**.

### Test Structure
```
tests/
├── unit/                   # Fast, isolated tests (no external dependencies)
│   ├── domain/            # Domain layer tests
│   ├── application/       # Application layer tests
│   └── infrastructure/    # Infrastructure layer tests
├── integration/           # Integration tests (with real services)
├── fixtures/              # Test data and sample files
└── conftest.py           # Shared fixtures and configuration
```

### Running Tests

```bash
# Run all tests
make test

# Run only unit tests (fast)
make test-unit

# Run only integration tests
make test-integration

# Run tests with coverage
make test-cov

# Run tests in watch mode
make test-watch

# Run specific test file
pytest tests/unit/domain/test_entities.py -v

# Run tests with specific markers
pytest -m "unit" -v
pytest -m "integration" -v
```

### Test Categories

- **Unit Tests** (`@pytest.mark.unit`): Fast, isolated tests with mocked dependencies
- **Integration Tests** (`@pytest.mark.integration`): Tests with real external services
- **Slow Tests** (`@pytest.mark.slow`): Performance and load tests

### Writing Tests

The dependency injection container makes testing easy:

```python
def test_process_document_use_case(mocked_container):
    """Test use case with all dependencies mocked"""
    use_case = mocked_container.process_document_use_case()

    # All dependencies are automatically mocked
    result = await use_case.execute(command)

    assert result.success is True
```

## 📊 Monitoring

### Structured Logging
All logs are structured JSON for CloudWatch:
```json
{
  "timestamp": "2025-08-07T10:30:00Z",
  "level": "INFO",
  "message": "Document processing completed",
  "user_id": "user123",
  "document_id": "doc456",
  "processed_rows": 1000,
  "duration_ms": 45000
}
```

### Metrics Collection
- Processing times
- Success/failure rates
- Error categorization
- Resource utilization

## 👨‍💻 Development Workflow

### Code Quality Standards

This project enforces high code quality standards with automated tools:

```bash
# Format code
make format

# Check formatting
make format-check

# Run all linting
make lint

# Type checking
make type-check

# Security scanning
make security

# Run pre-commit hooks
make pre-commit
```

### Pre-commit Hooks

The project uses pre-commit hooks to ensure code quality:

- **black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting and style checking
- **mypy**: Type checking
- **bandit**: Security scanning
- **pytest**: Run unit tests

### Development Commands

```bash
# Show all available commands
make help

# Setup development environment
make setup-dev

# Run tests with coverage
make test-cov

# Build Lambda deployment package
make build

# Build Docker image
make docker-build

# Clean build artifacts
make clean
```

### CI/CD Pipeline

The project includes a comprehensive CI/CD pipeline with GitHub Actions:

- **Automated Testing**: Unit and integration tests
- **Code Quality**: Linting, formatting, type checking
- **Security Scanning**: Dependency and code security checks
- **Build**: Lambda deployment package creation
- **Deploy**: Automated deployment to AWS Lambda

## 🔄 Data Flow

1. **S3 Upload:** CSV uploaded to `{user_id}/uploaded/{filename}.csv`
2. **Lambda Trigger:** S3 event triggers Lambda function
3. **Dependency Injection:** Container wires all dependencies
4. **Use Case Execution:** ProcessDocumentUseCase orchestrates the flow
5. **CSV Processing:** Parse, validate, and clean data
6. **Embedding Generation:** OpenAI generates vector embeddings
7. **Storage:** Save to Supabase with pgvector
8. **File Management:** Move to processed/failed folder
9. **Event Publishing:** Publish completion events

## 🛡️ Security

- **Multi-tenant isolation:** Complete data separation by user_id
- **Input validation:** CSV schema and content validation
- **Error handling:** Comprehensive error classification
- **Audit logging:** All operations logged with context
- **Secret management:** Environment variables for sensitive data

## 🚀 Deployment

### Docker Build
```bash
docker build -t document-ingestion .
```

### AWS Lambda
```bash
# Package for Lambda
zip -r function.zip src/ requirements.txt

# Deploy with AWS CLI
aws lambda update-function-code \
  --function-name document-ingestion \
  --zip-file fileb://function.zip
```

## 🔧 Troubleshooting

### Common Issues

1. **"OpenAI API key not found"**
   - Check `OPENAI_API_KEY` environment variable

2. **"Supabase connection failed"**
   - Verify `SUPABASE_URL` and `SUPABASE_SERVICE_ROLE_KEY`

3. **"Dependency injection failed"**
   - Run `python src/infrastructure/test_di.py` to diagnose

4. **"Lambda timeout"**
   - Reduce `MAX_ROWS` or increase Lambda timeout

## 📚 Further Reading

- [Clean Architecture by Robert Martin](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [dependency-injector Documentation](https://python-dependency-injector.ets-labs.org/)
- [Supabase pgvector Guide](https://supabase.com/docs/guides/database/extensions/pgvector)
- [OpenAI Embeddings API](https://platform.openai.com/docs/guides/embeddings)

## 🤝 Contributing

1. Follow clean architecture principles
2. Add tests for new features
3. Update documentation
4. Use dependency injection for new components
5. Maintain separation of concerns
