#!/bin/bash

# End-to-End Pipeline Test Script for LocalStack
# This script tests the complete document ingestion pipeline

set -e

# Configuration
LOCALSTACK_ENDPOINT="http://localhost:4566"
BUCKET_NAME="localstack-ezychat-documents"
TEST_USER_ID="test-user-123"
TEST_CSV_FILE="test-products.csv"
LAMBDA_FUNCTION_NAME="local-lambda-api-document-ingestion-lambda"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Set AWS environment for LocalStack
setup_aws_env() {
    export AWS_ENDPOINT_URL="${LOCALSTACK_ENDPOINT}"
    export AWS_ACCESS_KEY_ID="test"
    export AWS_SECRET_ACCESS_KEY="test"
    export AWS_DEFAULT_REGION="us-east-1"
}

# Create test CSV file
create_test_csv() {
    log_info "Creating test CSV file..."
    
    cat > "${TEST_CSV_FILE}" << EOF
name,description,price,category,brand,sku
iPhone 15 Pro,Latest Apple smartphone with titanium design,999.99,Electronics,Apple,IPHONE15PRO
MacBook Air M2,Lightweight laptop with M2 chip,1199.99,Computers,Apple,MBA-M2-13
Samsung Galaxy S24,Android flagship smartphone,899.99,Electronics,Samsung,SGS24-128
Dell XPS 13,Premium ultrabook laptop,1099.99,Computers,Dell,XPS13-9320
Sony WH-1000XM5,Noise-canceling wireless headphones,399.99,Audio,Sony,WH1000XM5
iPad Pro 12.9,Professional tablet with M2 chip,1099.99,Tablets,Apple,IPADPRO129
Microsoft Surface Pro 9,2-in-1 laptop tablet,999.99,Computers,Microsoft,SURFACEPRO9
AirPods Pro 2,Wireless earbuds with active noise cancellation,249.99,Audio,Apple,AIRPODSPRO2
Google Pixel 8,Google's flagship Android phone,699.99,Electronics,Google,PIXEL8-128
Nintendo Switch OLED,Gaming console with OLED screen,349.99,Gaming,Nintendo,SWITCH-OLED
EOF
    
    log_success "Test CSV file created: ${TEST_CSV_FILE}"
}

# Upload test file to S3
upload_test_file() {
    log_info "Uploading test file to S3..."
    
    local s3_key="${TEST_USER_ID}/uploaded/${TEST_CSV_FILE}"
    
    if aws s3 cp "${TEST_CSV_FILE}" "s3://${BUCKET_NAME}/${s3_key}"; then
        log_success "Test file uploaded to s3://${BUCKET_NAME}/${s3_key}"
        return 0
    else
        log_error "Failed to upload test file"
        return 1
    fi
}

# Check if Lambda function exists
check_lambda_function() {
    log_info "Checking if Lambda function exists..."
    
    if aws lambda get-function --function-name "${LAMBDA_FUNCTION_NAME}" > /dev/null 2>&1; then
        log_success "Lambda function found: ${LAMBDA_FUNCTION_NAME}"
        return 0
    else
        log_error "Lambda function not found: ${LAMBDA_FUNCTION_NAME}"
        return 1
    fi
}

# Check if S3 bucket exists
check_s3_bucket() {
    log_info "Checking if S3 bucket exists..."
    
    if aws s3 ls "s3://${BUCKET_NAME}" > /dev/null 2>&1; then
        log_success "S3 bucket found: ${BUCKET_NAME}"
        return 0
    else
        log_error "S3 bucket not found: ${BUCKET_NAME}"
        return 1
    fi
}

# Wait for Lambda execution
wait_for_processing() {
    log_info "Waiting for Lambda processing to complete..."
    
    local max_wait=60  # 60 seconds
    local wait_time=0
    local check_interval=5
    
    while [ $wait_time -lt $max_wait ]; do
        # Check if processed file exists
        if aws s3 ls "s3://${BUCKET_NAME}/${TEST_USER_ID}/processed/" > /dev/null 2>&1; then
            log_success "Processing completed - files found in processed/ folder"
            return 0
        fi
        
        # Check if failed file exists
        if aws s3 ls "s3://${BUCKET_NAME}/${TEST_USER_ID}/failed/" > /dev/null 2>&1; then
            log_warning "Processing failed - files found in failed/ folder"
            return 1
        fi
        
        log_info "Still processing... (${wait_time}s/${max_wait}s)"
        sleep $check_interval
        wait_time=$((wait_time + check_interval))
    done
    
    log_warning "Processing timeout reached (${max_wait}s)"
    return 1
}

# Check Lambda logs
check_lambda_logs() {
    log_info "Checking Lambda function logs..."
    
    local log_group="/aws/lambda/${LAMBDA_FUNCTION_NAME}"
    
    # Get recent log streams
    local log_streams=$(aws logs describe-log-streams \
        --log-group-name "${log_group}" \
        --order-by LastEventTime \
        --descending \
        --max-items 1 \
        --query 'logStreams[0].logStreamName' \
        --output text 2>/dev/null)
    
    if [ "$log_streams" != "None" ] && [ -n "$log_streams" ]; then
        log_info "Recent Lambda logs:"
        aws logs get-log-events \
            --log-group-name "${log_group}" \
            --log-stream-name "${log_streams}" \
            --query 'events[*].message' \
            --output text 2>/dev/null | tail -20
    else
        log_warning "No recent Lambda logs found"
    fi
}

# List S3 bucket contents
list_bucket_contents() {
    log_info "Listing S3 bucket contents..."
    
    aws s3 ls "s3://${BUCKET_NAME}/" --recursive
}

# Cleanup test files
cleanup() {
    log_info "Cleaning up test files..."
    
    # Remove local test file
    if [ -f "${TEST_CSV_FILE}" ]; then
        rm "${TEST_CSV_FILE}"
        log_success "Local test file removed"
    fi
    
    # Remove S3 test files
    aws s3 rm "s3://${BUCKET_NAME}/${TEST_USER_ID}/" --recursive > /dev/null 2>&1 || true
    log_success "S3 test files removed"
}

# Main test function
main() {
    log_info "Starting End-to-End Pipeline Test"
    log_info "=================================="
    
    setup_aws_env
    
    # Pre-flight checks
    if ! check_s3_bucket; then
        log_error "S3 bucket check failed"
        exit 1
    fi
    
    if ! check_lambda_function; then
        log_error "Lambda function check failed"
        exit 1
    fi
    
    # Create and upload test file
    create_test_csv
    
    if ! upload_test_file; then
        cleanup
        exit 1
    fi
    
    # Wait for processing
    log_info "Monitoring processing..."
    if wait_for_processing; then
        log_success "Pipeline test completed successfully!"
        
        log_info "Final bucket contents:"
        list_bucket_contents
    else
        log_error "Pipeline test failed"
        
        log_info "Checking logs for debugging..."
        check_lambda_logs
        
        log_info "Current bucket contents:"
        list_bucket_contents
    fi
    
    # Cleanup
    cleanup
    
    log_info "=================================="
    log_info "End-to-End Pipeline Test Complete"
}

# Handle script interruption
trap cleanup EXIT

# Run main function
main "$@"
