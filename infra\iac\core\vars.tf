variable "env" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
}

variable "tags" {
  description = "Map of tags to add to resources"
  type        = map(string)
  default     = {}
}

# Network Configuration Variables
variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_count" {
  description = "Number of public subnets to create"
  type        = number
  default     = 2

  validation {
    condition     = var.public_subnet_count >= 1 && var.public_subnet_count <= 6
    error_message = "Public subnet count must be between 1 and 6."
  }
}

variable "private_subnet_count" {
  description = "Number of private subnets to create"
  type        = number
  default     = 2

  validation {
    condition     = var.private_subnet_count >= 0 && var.private_subnet_count <= 6
    error_message = "Private subnet count must be between 0 and 6."
  }
}

variable "enable_nat_gateway" {
  description = "Enable NAT Gateway for private subnets"
  type        = bool
  default     = true
}

variable "nat_gateway_strategy" {
  description = "NAT Gateway deployment strategy: 'single' for cost-optimized, 'per_az' for high availability"
  type        = string
  default     = "single"

  validation {
    condition     = contains(["single", "per_az"], var.nat_gateway_strategy)
    error_message = "NAT Gateway strategy must be either 'single' or 'per_az'."
  }
}

variable "use_private_subnets" {
  description = "Whether to deploy ECS services in private subnets (recommended for production)"
  type        = bool
  default     = false
}

# Domain Configuration Variables
variable "domain_name" {
  description = "The root domain name (e.g., ezychat.ai, mycompany.com)"
  type        = string
  default     = "ezychat.ai"
}

variable "api_subdomain" {
  description = "The API subdomain prefix (e.g., api)"
  type        = string
  default     = "api"
}

variable "app_subdomain" {
  description = "The App subdomain prefix (e.g., api)"
  type        = string
  default     = "app"
}

# Document Ingestion Configuration Variables
variable "openai_api_key_secret_arn" {
  description = "ARN of the OpenAI API key secret in AWS Secrets Manager"
  type        = string
  default     = ""
}

variable "supabase_url_secret_arn" {
  description = "ARN of the Supabase URL secret in AWS Secrets Manager"
  type        = string
  default     = ""
}

variable "supabase_key_secret_arn" {
  description = "ARN of the Supabase key secret in AWS Secrets Manager"
  type        = string
  default     = ""
}

# Additional Supabase secrets for clean architecture
variable "supabase_service_role_key_secret_arn" {
  description = "ARN of the Supabase service role key secret in AWS Secrets Manager"
  type        = string
  default     = ""
}

variable "supabase_anon_key_secret_arn" {
  description = "ARN of the Supabase anonymous key secret in AWS Secrets Manager"
  type        = string
  default     = ""
}

# LocalStack Configuration Variables
variable "localstack_endpoint" {
  description = "LocalStack endpoint URL for local development"
  type        = string
  default     = "http://localhost:4566"
}

variable "is_localstack" {
  description = "Flag to indicate if deploying to LocalStack for local development"
  type        = bool
  default     = false
}

variable "localstack_ecr_registry" {
  description = "LocalStack ECR registry URL for container images"
  type        = string
  default     = "localhost:4566"
}

# Document Processing Configuration
variable "document_processing_config" {
  description = "Configuration for document processing pipeline"
  type = object({
    max_rows                = optional(number, 10000)
    retry_attempts          = optional(number, 3)
    retry_min_wait         = optional(number, 1)
    retry_max_wait         = optional(number, 60)
    embedding_batch_size   = optional(number, 100)
    supabase_batch_size    = optional(number, 50)
    similarity_threshold   = optional(number, 0.8)
    max_search_results     = optional(number, 20)
    lambda_timeout         = optional(number, 900)
    lambda_memory_size     = optional(number, 2048)
    log_retention_days     = optional(number, 14)
  })
  default = {}
}

# Secrets Configuration for LocalStack and AWS
variable "secrets_config" {
  description = "Configuration for AWS Secrets Manager secrets"
  type = object({
    openai_api_key_secret_name           = optional(string, "lambda/openai-api-key")
    supabase_url_secret_name            = optional(string, "lambda/supabase-url")
    supabase_service_role_key_secret_name = optional(string, "lambda/supabase-service-role-key")
    supabase_anon_key_secret_name       = optional(string, "lambda/supabase-anon-key")
  })
  default = {}
}
