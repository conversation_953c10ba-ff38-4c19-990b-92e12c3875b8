#!/bin/bash

# LocalStack Deployment Script for Document Ingestion Pipeline
# This script builds, pushes, and deploys the document ingestion Lambda to LocalStack

set -e

# Configuration
LOCALSTACK_ENDPOINT="http://localhost:4566"
ECR_REGISTRY="localhost:4566"
IMAGE_NAME="lambda-document-ingestion"
IMAGE_TAG="latest"
FULL_IMAGE_URI="${ECR_REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
TERRAFORM_DIR="$(dirname "$0")/.."
DOCUMENT_INGESTION_DIR="../../../apps/document-ingestion"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if LocalStack is running
check_localstack() {
    log_info "Checking if LocalStack is running..."
    if curl -s "${LOCALSTACK_ENDPOINT}/health" > /dev/null 2>&1; then
        log_success "LocalStack is running at ${LOCALSTACK_ENDPOINT}"
    else
        log_error "LocalStack is not running. Please start LocalStack first:"
        log_error "  docker run -d --rm -it -p 4566:4566 -e SERVICES=s3,lambda,iam,ecr,secretsmanager localstack/localstack"
        exit 1
    fi
}

# Check required tools
check_dependencies() {
    log_info "Checking required dependencies..."
    
    local missing_deps=()
    
    if ! command -v docker &> /dev/null; then
        missing_deps+=("docker")
    fi
    
    if ! command -v tflocal &> /dev/null; then
        missing_deps+=("tflocal (install with: pip install terraform-local)")
    fi
    
    if ! command -v aws &> /dev/null; then
        missing_deps+=("aws-cli")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing required dependencies:"
        for dep in "${missing_deps[@]}"; do
            log_error "  - $dep"
        done
        exit 1
    fi
    
    log_success "All dependencies are available"
}

# Build Docker image
build_image() {
    log_info "Building Docker image for document ingestion Lambda..."
    
    cd "${DOCUMENT_INGESTION_DIR}"
    
    if docker build -t "${IMAGE_NAME}:${IMAGE_TAG}" .; then
        log_success "Docker image built successfully: ${IMAGE_NAME}:${IMAGE_TAG}"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
    
    cd - > /dev/null
}

# Create ECR repository in LocalStack
create_ecr_repository() {
    log_info "Creating ECR repository in LocalStack..."
    
    export AWS_ENDPOINT_URL="${LOCALSTACK_ENDPOINT}"
    export AWS_ACCESS_KEY_ID="test"
    export AWS_SECRET_ACCESS_KEY="test"
    export AWS_DEFAULT_REGION="us-east-1"
    
    # Check if repository exists
    if aws ecr describe-repositories --repository-names "${IMAGE_NAME}" > /dev/null 2>&1; then
        log_warning "ECR repository ${IMAGE_NAME} already exists"
    else
        if aws ecr create-repository --repository-name "${IMAGE_NAME}"; then
            log_success "ECR repository created: ${IMAGE_NAME}"
        else
            log_error "Failed to create ECR repository"
            exit 1
        fi
    fi
}

# Push image to LocalStack ECR
push_image() {
    log_info "Pushing Docker image to LocalStack ECR..."
    
    # Tag image for LocalStack ECR
    docker tag "${IMAGE_NAME}:${IMAGE_TAG}" "${FULL_IMAGE_URI}"
    
    # Push to LocalStack ECR (no authentication needed)
    if docker push "${FULL_IMAGE_URI}"; then
        log_success "Image pushed successfully: ${FULL_IMAGE_URI}"
    else
        log_error "Failed to push image to LocalStack ECR"
        exit 1
    fi
}

# Deploy infrastructure with Terraform
deploy_infrastructure() {
    log_info "Deploying infrastructure with tflocal..."
    
    cd "${TERRAFORM_DIR}"
    
    # Initialize Terraform
    if tflocal init; then
        log_success "Terraform initialized"
    else
        log_error "Failed to initialize Terraform"
        exit 1
    fi
    
    # Plan deployment
    log_info "Planning Terraform deployment..."
    if tflocal plan \
        -var="environment=local" \
        -var="is_localstack=true" \
        -var="localstack_endpoint=${LOCALSTACK_ENDPOINT}" \
        -var="localstack_ecr_registry=${ECR_REGISTRY}" \
        -out=localstack.tfplan; then
        log_success "Terraform plan completed"
    else
        log_error "Terraform plan failed"
        exit 1
    fi
    
    # Apply deployment
    log_info "Applying Terraform deployment..."
    if tflocal apply -auto-approve localstack.tfplan; then
        log_success "Infrastructure deployed successfully"
    else
        log_error "Terraform apply failed"
        exit 1
    fi
    
    cd - > /dev/null
}

# Test deployment
test_deployment() {
    log_info "Testing deployment..."
    
    # Check if Lambda function exists
    export AWS_ENDPOINT_URL="${LOCALSTACK_ENDPOINT}"
    export AWS_ACCESS_KEY_ID="test"
    export AWS_SECRET_ACCESS_KEY="test"
    export AWS_DEFAULT_REGION="us-east-1"
    
    if aws lambda get-function --function-name "local-lambda-api-document-ingestion-lambda" > /dev/null 2>&1; then
        log_success "Lambda function deployed successfully"
    else
        log_warning "Lambda function not found or not accessible"
    fi
    
    # Check if S3 bucket exists
    if aws s3 ls s3://localstack-ezychat-documents > /dev/null 2>&1; then
        log_success "S3 bucket created successfully"
    else
        log_warning "S3 bucket not found or not accessible"
    fi
}

# Main deployment function
main() {
    log_info "Starting LocalStack deployment for Document Ingestion Pipeline"
    log_info "=================================================="
    
    check_dependencies
    check_localstack
    build_image
    create_ecr_repository
    push_image
    deploy_infrastructure
    test_deployment
    
    log_success "=================================================="
    log_success "LocalStack deployment completed successfully!"
    log_success "LocalStack endpoint: ${LOCALSTACK_ENDPOINT}"
    log_success "ECR image: ${FULL_IMAGE_URI}"
    log_success ""
    log_info "Next steps:"
    log_info "1. Test the pipeline: make localstack-test"
    log_info "2. Upload a test CSV file to trigger processing"
    log_info "3. Check logs: make localstack-logs"
}

# Run main function
main "$@"
