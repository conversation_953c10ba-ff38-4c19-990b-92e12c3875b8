# LocalStack Deployment Script for Document Ingestion Pipeline (PowerShell)
# This script builds, pushes, and deploys the document ingestion Lambda to LocalStack

param(
    [string]$LocalStackEndpoint = "http://localhost:4566",
    [string]$ECRRegistry = "localhost:4566",
    [string]$ImageName = "lambda-document-ingestion",
    [string]$ImageTag = "latest"
)

# Configuration
$FullImageURI = "$ECRRegistry/$ImageName`:$ImageTag"
$TerraformDir = Split-Path -Parent $PSScriptRoot
$DocumentIngestionDir = Join-Path $PSScriptRoot "..\..\..\..\apps\document-ingestion"

# Logging functions
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if LocalStack is running
function Test-LocalStack {
    Write-Info "Checking if LocalStack is running..."
    try {
        $response = Invoke-RestMethod -Uri "$LocalStackEndpoint/health" -Method Get -TimeoutSec 5
        Write-Success "LocalStack is running at $LocalStackEndpoint"
        return $true
    }
    catch {
        Write-Error "LocalStack is not running. Please start LocalStack first:"
        Write-Error "  docker run -d --rm -it -p 4566:4566 -e SERVICES=s3,lambda,iam,ecr,secretsmanager localstack/localstack"
        return $false
    }
}

# Check required tools
function Test-Dependencies {
    Write-Info "Checking required dependencies..."
    
    $missingDeps = @()
    
    if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
        $missingDeps += "docker"
    }
    
    if (-not (Get-Command tflocal -ErrorAction SilentlyContinue)) {
        $missingDeps += "tflocal (install with: pip install terraform-local)"
    }
    
    if (-not (Get-Command aws -ErrorAction SilentlyContinue)) {
        $missingDeps += "aws-cli"
    }
    
    if ($missingDeps.Count -gt 0) {
        Write-Error "Missing required dependencies:"
        foreach ($dep in $missingDeps) {
            Write-Error "  - $dep"
        }
        return $false
    }
    
    Write-Success "All dependencies are available"
    return $true
}

# Build Docker image
function Build-DockerImage {
    Write-Info "Building Docker image for document ingestion Lambda..."
    
    Push-Location $DocumentIngestionDir
    
    try {
        $result = docker build -t "$ImageName`:$ImageTag" .
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Docker image built successfully: $ImageName`:$ImageTag"
            return $true
        }
        else {
            Write-Error "Failed to build Docker image"
            return $false
        }
    }
    finally {
        Pop-Location
    }
}

# Create ECR repository in LocalStack
function New-ECRRepository {
    Write-Info "Creating ECR repository in LocalStack..."
    
    # Set AWS environment variables for LocalStack
    $env:AWS_ENDPOINT_URL = $LocalStackEndpoint
    $env:AWS_ACCESS_KEY_ID = "test"
    $env:AWS_SECRET_ACCESS_KEY = "test"
    $env:AWS_DEFAULT_REGION = "us-east-1"
    
    # Check if repository exists
    try {
        aws ecr describe-repositories --repository-names $ImageName 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Warning "ECR repository $ImageName already exists"
            return $true
        }
    }
    catch {
        # Repository doesn't exist, create it
    }
    
    try {
        aws ecr create-repository --repository-name $ImageName
        if ($LASTEXITCODE -eq 0) {
            Write-Success "ECR repository created: $ImageName"
            return $true
        }
        else {
            Write-Error "Failed to create ECR repository"
            return $false
        }
    }
    catch {
        Write-Error "Failed to create ECR repository: $_"
        return $false
    }
}

# Push image to LocalStack ECR
function Push-DockerImage {
    Write-Info "Pushing Docker image to LocalStack ECR..."
    
    # Tag image for LocalStack ECR
    docker tag "$ImageName`:$ImageTag" $FullImageURI
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to tag Docker image"
        return $false
    }
    
    # Push to LocalStack ECR (no authentication needed)
    docker push $FullImageURI
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Image pushed successfully: $FullImageURI"
        return $true
    }
    else {
        Write-Error "Failed to push image to LocalStack ECR"
        return $false
    }
}

# Deploy infrastructure with Terraform
function Deploy-Infrastructure {
    Write-Info "Deploying infrastructure with tflocal..."
    
    Push-Location $TerraformDir
    
    try {
        # Initialize Terraform
        tflocal init
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to initialize Terraform"
            return $false
        }
        Write-Success "Terraform initialized"
        
        # Plan deployment
        Write-Info "Planning Terraform deployment..."
        tflocal plan `
            -var="environment=local" `
            -var="is_localstack=true" `
            -var="localstack_endpoint=$LocalStackEndpoint" `
            -var="localstack_ecr_registry=$ECRRegistry" `
            -out=localstack.tfplan
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Terraform plan failed"
            return $false
        }
        Write-Success "Terraform plan completed"
        
        # Apply deployment
        Write-Info "Applying Terraform deployment..."
        tflocal apply -auto-approve localstack.tfplan
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Infrastructure deployed successfully"
            return $true
        }
        else {
            Write-Error "Terraform apply failed"
            return $false
        }
    }
    finally {
        Pop-Location
    }
}

# Test deployment
function Test-Deployment {
    Write-Info "Testing deployment..."
    
    # Set AWS environment variables for LocalStack
    $env:AWS_ENDPOINT_URL = $LocalStackEndpoint
    $env:AWS_ACCESS_KEY_ID = "test"
    $env:AWS_SECRET_ACCESS_KEY = "test"
    $env:AWS_DEFAULT_REGION = "us-east-1"
    
    # Check if Lambda function exists
    try {
        aws lambda get-function --function-name "local-lambda-api-document-ingestion-lambda" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Lambda function deployed successfully"
        }
        else {
            Write-Warning "Lambda function not found or not accessible"
        }
    }
    catch {
        Write-Warning "Lambda function not found or not accessible"
    }
    
    # Check if S3 bucket exists
    try {
        aws s3 ls s3://localstack-ezychat-documents 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "S3 bucket created successfully"
        }
        else {
            Write-Warning "S3 bucket not found or not accessible"
        }
    }
    catch {
        Write-Warning "S3 bucket not found or not accessible"
    }
}

# Main deployment function
function Main {
    Write-Info "Starting LocalStack deployment for Document Ingestion Pipeline"
    Write-Info "=================================================="
    
    if (-not (Test-Dependencies)) { exit 1 }
    if (-not (Test-LocalStack)) { exit 1 }
    if (-not (Build-DockerImage)) { exit 1 }
    if (-not (New-ECRRepository)) { exit 1 }
    if (-not (Push-DockerImage)) { exit 1 }
    if (-not (Deploy-Infrastructure)) { exit 1 }
    Test-Deployment
    
    Write-Success "=================================================="
    Write-Success "LocalStack deployment completed successfully!"
    Write-Success "LocalStack endpoint: $LocalStackEndpoint"
    Write-Success "ECR image: $FullImageURI"
    Write-Success ""
    Write-Info "Next steps:"
    Write-Info "1. Test the pipeline: make localstack-test"
    Write-Info "2. Upload a test CSV file to trigger processing"
    Write-Info "3. Check logs: make localstack-logs"
}

# Run main function
Main
