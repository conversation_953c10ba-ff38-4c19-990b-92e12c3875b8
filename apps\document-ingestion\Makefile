# Makefile for Document Ingestion Pipeline
# Clean Architecture AWS Lambda Project

.PHONY: help install install-dev test test-unit test-integration lint format type-check security clean build docker-build docker-run deploy setup-dev

# Default target
help:
	@echo "Document Ingestion Pipeline - Development Commands"
	@echo "=================================================="
	@echo ""
	@echo "Setup Commands:"
	@echo "  install          Install production dependencies"
	@echo "  install-dev      Install development dependencies"
	@echo "  setup-dev        Complete development environment setup"
	@echo ""
	@echo "Testing Commands:"
	@echo "  test             Run all tests"
	@echo "  test-unit        Run unit tests only"
	@echo "  test-integration Run integration tests only"
	@echo "  test-cov         Run tests with coverage report"
	@echo "  test-watch       Run tests in watch mode"
	@echo ""
	@echo "Code Quality Commands:"
	@echo "  lint             Run all linting checks"
	@echo "  format           Format code with black and isort"
	@echo "  type-check       Run mypy type checking"
	@echo "  security         Run security checks with bandit"
	@echo "  pre-commit       Run pre-commit hooks"
	@echo ""
	@echo "Build Commands:"
	@echo "  clean            Clean build artifacts"
	@echo "  build            Build Lambda deployment package"
	@echo "  docker-build     Build Docker image"
	@echo "  docker-run       Run Docker container locally"
	@echo ""
	@echo "Deployment Commands:"
	@echo "  deploy           Deploy to AWS Lambda"
	@echo "  deploy-dev       Deploy to development environment"
	@echo "  deploy-prod      Deploy to production environment"
	@echo ""
	@echo "LocalStack Commands (Local AWS Testing):"
	@echo "  localstack-start     Start LocalStack services"
	@echo "  localstack-stop      Stop LocalStack services"
	@echo "  localstack-status    Check LocalStack status"
	@echo "  localstack-deploy    Deploy infrastructure to LocalStack"
	@echo "  localstack-destroy   Destroy LocalStack infrastructure"
	@echo "  localstack-test      Run integration tests against LocalStack"
	@echo "  localstack-setup     Start LocalStack and deploy infrastructure"
	@echo "  localstack-reset     Reset LocalStack environment"
	@echo "  localstack-logs      Show LocalStack logs"
	@echo "  localstack-s3-list   List S3 buckets in LocalStack"
	@echo "  localstack-lambda-list List Lambda functions in LocalStack"

# Python and pip commands
PYTHON := python
PIP := pip
PYTEST := pytest

# Project directories
SRC_DIR := src
TEST_DIR := tests
BUILD_DIR := build
DIST_DIR := dist

# Installation commands
install:
	$(PIP) install -r requirements.txt

install-dev:
	$(PIP) install -e ".[dev]"
	pre-commit install

setup-dev: install-dev
	@echo "Setting up development environment..."
	@echo "Creating .env file from .env.example..."
	@if [ ! -f .env ]; then cp .env.example .env; fi
	@echo "Installing pre-commit hooks..."
	pre-commit install
	@echo "Running initial tests..."
	$(MAKE) test-unit
	@echo "Development environment setup complete!"

# Testing commands
test:
	$(PYTEST) $(TEST_DIR) -v

test-unit:
	$(PYTEST) $(TEST_DIR)/unit -v -m "unit"

test-integration:
	$(PYTEST) $(TEST_DIR)/integration -v -m "integration"

test-cov:
	$(PYTEST) $(TEST_DIR) --cov=$(SRC_DIR) --cov-report=html --cov-report=term-missing

test-watch:
	$(PYTEST) $(TEST_DIR) -f

test-fast:
	$(PYTEST) $(TEST_DIR) -x -v --tb=short

# Code quality commands
lint: lint-flake8 lint-mypy lint-bandit

lint-flake8:
	flake8 $(SRC_DIR) $(TEST_DIR)

lint-mypy:
	mypy $(SRC_DIR)

lint-bandit:
	bandit -r $(SRC_DIR)

format:
	black $(SRC_DIR) $(TEST_DIR)
	isort $(SRC_DIR) $(TEST_DIR)

format-check:
	black --check $(SRC_DIR) $(TEST_DIR)
	isort --check-only $(SRC_DIR) $(TEST_DIR)

type-check:
	mypy $(SRC_DIR)

security:
	bandit -r $(SRC_DIR)
	safety check

pre-commit:
	pre-commit run --all-files

# Dependency management
deps-update:
	$(PIP) list --outdated
	@echo "Run 'pip install --upgrade <package>' to update specific packages"

deps-check:
	$(PIP) check
	safety check

# Clean commands
clean:
	rm -rf $(BUILD_DIR)
	rm -rf $(DIST_DIR)
	rm -rf *.egg-info
	rm -rf .pytest_cache
	rm -rf .mypy_cache
	rm -rf .coverage
	rm -rf htmlcov
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete

clean-all: clean
	rm -rf .venv
	rm -rf node_modules

# Build commands
build: clean
	@echo "Building Lambda deployment package..."
	mkdir -p $(BUILD_DIR)
	cp -r $(SRC_DIR) $(BUILD_DIR)/
	cp requirements.txt $(BUILD_DIR)/
	$(PIP) install -r requirements.txt -t $(BUILD_DIR)/
	cd $(BUILD_DIR) && zip -r ../lambda-deployment.zip .
	@echo "Deployment package created: lambda-deployment.zip"

build-docker:
	docker build -t document-ingestion:latest .

# Docker commands
docker-build:
	docker build -t document-ingestion:latest .

docker-run:
	docker run -p 8080:8080 --env-file .env document-ingestion:latest

docker-test:
	docker run --rm -v $(PWD):/app -w /app python:3.12 make test

# AWS deployment commands
deploy: build
	@echo "Deploying to AWS Lambda..."
	aws lambda update-function-code \
		--function-name document-ingestion \
		--zip-file fileb://lambda-deployment.zip
	@echo "Deployment complete!"

deploy-dev: build
	@echo "Deploying to development environment..."
	aws lambda update-function-code \
		--function-name document-ingestion-dev \
		--zip-file fileb://lambda-deployment.zip

deploy-prod: build
	@echo "Deploying to production environment..."
	aws lambda update-function-code \
		--function-name document-ingestion-prod \
		--zip-file fileb://lambda-deployment.zip

# Development utilities
run-local:
	$(PYTHON) -c "from $(SRC_DIR).presentation.lambda_handler import lambda_handler; print('Lambda handler loaded successfully')"

check-deps:
	$(PYTHON) -c "from $(SRC_DIR).infrastructure.dependency_injection import ApplicationContainer; c = ApplicationContainer(); print('Dependency injection working!')"

validate-config:
	$(PYTHON) -c "from $(SRC_DIR).infrastructure.configuration import EnvironmentConfiguration; c = EnvironmentConfiguration(); print('Configuration valid!')"

# Documentation
docs-serve:
	@echo "Serving documentation..."
	@echo "Open http://localhost:8000 to view documentation"
	$(PYTHON) -m http.server 8000 --directory docs

# CI/CD helpers
ci-install:
	$(PIP) install -e ".[dev,test]"

ci-test:
	$(PYTEST) $(TEST_DIR) --cov=$(SRC_DIR) --cov-report=xml --junitxml=test-results.xml

ci-lint:
	flake8 $(SRC_DIR) $(TEST_DIR) --output-file=flake8-report.txt
	mypy $(SRC_DIR) --junit-xml=mypy-report.xml
	bandit -r $(SRC_DIR) -f json -o bandit-report.json

# Performance testing
perf-test:
	@echo "Running performance tests..."
	$(PYTEST) $(TEST_DIR) -m "slow" -v

# Environment management
env-create:
	python -m venv .venv
	@echo "Virtual environment created. Activate with: source .venv/bin/activate (Linux/Mac) or .venv\\Scripts\\activate (Windows)"

env-activate:
	@echo "To activate virtual environment:"
	@echo "Linux/Mac: source .venv/bin/activate"
	@echo "Windows: .venv\\Scripts\\activate"

# LocalStack support for local AWS testing
LOCALSTACK_ENDPOINT := http://localhost:4566
LOCALSTACK_SERVICES := s3,lambda,iam,cloudformation
TERRAFORM_DIR := ../../infra/terraform/document-ingestion

localstack-start:
	@echo "Starting LocalStack services..."
	@if command -v docker >/dev/null 2>&1; then \
		docker run -d --rm -it \
			-p 4566:4566 \
			-e SERVICES=$(LOCALSTACK_SERVICES) \
			-e DEBUG=1 \
			-e DATA_DIR=/tmp/localstack/data \
			-e DOCKER_HOST=unix:///var/run/docker.sock \
			-v /var/run/docker.sock:/var/run/docker.sock \
			--name localstack-document-ingestion \
			localstack/localstack:latest; \
		echo "LocalStack started on $(LOCALSTACK_ENDPOINT)"; \
		echo "Waiting for LocalStack to be ready..."; \
		sleep 10; \
	else \
		echo "Docker is required to run LocalStack"; \
		exit 1; \
	fi

localstack-stop:
	@echo "Stopping LocalStack..."
	@docker stop localstack-document-ingestion || true
	@echo "LocalStack stopped"

localstack-status:
	@echo "Checking LocalStack status..."
	@if docker ps | grep -q localstack-document-ingestion; then \
		echo "✅ LocalStack is running"; \
		curl -s $(LOCALSTACK_ENDPOINT)/health | python -m json.tool || echo "Health check failed"; \
	else \
		echo "❌ LocalStack is not running"; \
	fi

localstack-deploy:
	@echo "Deploying to LocalStack using tflocal..."
	@if command -v tflocal >/dev/null 2>&1; then \
		cd $(TERRAFORM_DIR) && \
		tflocal init && \
		tflocal plan -var="environment=local" -var="localstack_endpoint=$(LOCALSTACK_ENDPOINT)" && \
		tflocal apply -auto-approve -var="environment=local" -var="localstack_endpoint=$(LOCALSTACK_ENDPOINT)"; \
	else \
		echo "tflocal is required. Install with: pip install terraform-local"; \
		exit 1; \
	fi

localstack-destroy:
	@echo "Destroying LocalStack infrastructure..."
	@if command -v tflocal >/dev/null 2>&1; then \
		cd $(TERRAFORM_DIR) && \
		tflocal destroy -auto-approve -var="environment=local" -var="localstack_endpoint=$(LOCALSTACK_ENDPOINT)"; \
	else \
		echo "tflocal is required. Install with: pip install terraform-local"; \
		exit 1; \
	fi

localstack-test:
	@echo "Running integration tests against LocalStack..."
	@export AWS_ENDPOINT_URL=$(LOCALSTACK_ENDPOINT) && \
	export AWS_ACCESS_KEY_ID=test && \
	export AWS_SECRET_ACCESS_KEY=test && \
	export AWS_DEFAULT_REGION=us-east-1 && \
	export ENVIRONMENT=local && \
	$(PYTEST) $(TEST_DIR)/integration -v -m "integration" --tb=short

localstack-setup: localstack-start localstack-deploy
	@echo "LocalStack setup complete!"
	@echo "Services available at: $(LOCALSTACK_ENDPOINT)"
	@echo "Run 'make localstack-test' to test against LocalStack"

localstack-reset: localstack-stop localstack-start localstack-deploy
	@echo "LocalStack reset complete!"

# LocalStack utilities
localstack-logs:
	@echo "Showing LocalStack logs..."
	@docker logs localstack-document-ingestion

localstack-shell:
	@echo "Opening shell in LocalStack container..."
	@docker exec -it localstack-document-ingestion /bin/bash

localstack-s3-list:
	@echo "Listing S3 buckets in LocalStack..."
	@AWS_ENDPOINT_URL=$(LOCALSTACK_ENDPOINT) AWS_ACCESS_KEY_ID=test AWS_SECRET_ACCESS_KEY=test aws s3 ls

localstack-lambda-list:
	@echo "Listing Lambda functions in LocalStack..."
	@AWS_ENDPOINT_URL=$(LOCALSTACK_ENDPOINT) AWS_ACCESS_KEY_ID=test AWS_SECRET_ACCESS_KEY=test aws lambda list-functions

# Version management
version:
	@echo "Current version: $(shell grep version pyproject.toml | head -1 | cut -d'"' -f2)"

bump-version:
	@echo "Bumping version..."
	@echo "Edit pyproject.toml to update version number"
