# Document Ingestion Pipeline - Terraform Infrastructure

This document describes the comprehensive Terraform Infrastructure as Code (IaC) setup for the document-ingestion pipeline that uses clean architecture principles.

## Overview

The document ingestion pipeline is designed to:
- Process CSV files uploaded to S3 buckets
- Generate embeddings using OpenAI API
- Store processed data in Supabase
- Support multi-tenant architecture with user-specific folders
- Work seamlessly in both AWS and LocalStack environments

## Architecture

### Clean Architecture Implementation
- **Presentation Layer**: `src/presentation/lambda_handler.py` - AWS Lambda handler
- **Application Layer**: Use cases and orchestration logic
- **Domain Layer**: Business entities and rules
- **Infrastructure Layer**: External dependencies (S3, OpenAI, Supabase)

### Multi-Tenant Folder Structure
```
s3://bucket-name/
├── {user_id}/
│   ├── uploaded/     # CSV files uploaded by users
│   ├── processed/    # Successfully processed files
│   └── failed/       # Failed processing files
```

## Infrastructure Components

### 1. Lambda Function
- **Handler**: `src.presentation.lambda_handler.lambda_handler`
- **Runtime**: Container-based (Python 3.12)
- **Memory**: 2048 MB (configurable)
- **Timeout**: 900 seconds (15 minutes)
- **Triggers**: S3 ObjectCreated events for CSV files

### 2. S3 Bucket
- **Name**: `{env}-ezychat-documents` (AWS) or `localstack-ezychat-documents` (LocalStack)
- **Versioning**: Enabled
- **Lifecycle**: 30 days → IA, 90 days → Glacier, 7 years retention
- **Triggers**: Lambda function on CSV uploads

### 3. Secrets Manager
- OpenAI API Key
- Supabase URL
- Supabase Service Role Key
- Supabase Anonymous Key

### 4. IAM Policies
- S3 bucket access (read/write/delete)
- Secrets Manager access
- CloudWatch Logs access
- X-Ray tracing access

## Configuration Files

### 1. Lambda Services Configuration
**File**: `configs/lambda_services.yaml`

Key configuration for document-ingestion-lambda:
- Container image URI (dynamic based on environment)
- Environment variables with template substitution
- IAM policies for required permissions
- Memory, timeout, and logging settings

### 2. Terraform Variables
**File**: `vars.tf`

New variables added:
- `is_localstack`: Flag for LocalStack deployment
- `localstack_endpoint`: LocalStack endpoint URL
- `document_processing_config`: Processing parameters
- `secrets_config`: Secrets Manager configuration

### 3. LocalStack Resources
**File**: `localstack.tf`

LocalStack-specific resources:
- ECR repository for container images
- Secrets Manager secrets with test values
- Conditional resource creation

## Deployment

### AWS Deployment
```bash
# Initialize and plan
terraform init
terraform plan -var="env=dev"

# Apply
terraform apply -var="env=dev"
```

### LocalStack Deployment
```bash
# Start LocalStack
docker run -d --rm -it -p 4566:4566 \
  -e SERVICES=s3,lambda,iam,ecr,secretsmanager \
  localstack/localstack

# Deploy infrastructure
cd infra/iac/core
./scripts/localstack-deploy.sh

# Or use tflocal directly
tflocal init
tflocal plan -var="environment=local" -var="is_localstack=true"
tflocal apply -auto-approve
```

### Application-Level Commands
```bash
# From apps/document-ingestion/
make localstack-setup          # Complete LocalStack setup
make localstack-deploy-infra    # Deploy infrastructure only
make localstack-build-push      # Build and push Docker image
make localstack-test           # Run integration tests
```

## Testing

### End-to-End Pipeline Test
```bash
# Run comprehensive pipeline test
cd infra/iac/core
./scripts/test-pipeline.sh
```

The test script:
1. Creates a test CSV file with sample product data
2. Uploads to S3 bucket (`test-user-123/uploaded/`)
3. Monitors Lambda execution
4. Checks for processed files in `processed/` or `failed/` folders
5. Displays logs and bucket contents
6. Cleans up test files

### Manual Testing
```bash
# Upload test file
aws s3 cp test.csv s3://bucket-name/user123/uploaded/ \
  --endpoint-url http://localhost:4566

# Check Lambda logs
aws logs describe-log-groups \
  --endpoint-url http://localhost:4566

# List bucket contents
aws s3 ls s3://bucket-name/ --recursive \
  --endpoint-url http://localhost:4566
```

## Template Variables

The configuration uses template variables that are dynamically replaced:

- `{{DOCUMENT_INGESTION_IMAGE_URI}}`: Container image URI
- `{{ENVIRONMENT}}`: Environment name (dev, uat, prod, local)
- `{{AWS_REGION}}`: Current AWS region
- `{{DOCUMENT_BUCKET_NAME}}`: S3 bucket name
- `{{OPENAI_API_KEY_ARN}}`: OpenAI API key secret ARN
- `{{SUPABASE_URL_ARN}}`: Supabase URL secret ARN
- `{{SUPABASE_SERVICE_ROLE_KEY_ARN}}`: Supabase service role key ARN
- `{{SUPABASE_ANON_KEY_ARN}}`: Supabase anonymous key ARN

## Environment-Specific Configurations

### LocalStack Overrides
- Reduced memory and timeout for faster testing
- Force destroy enabled for S3 bucket
- Test secrets with placeholder values
- LocalStack ECR registry URL

### Production Settings
- Full memory allocation (2048 MB)
- Extended timeout (900 seconds)
- Real secrets from AWS Secrets Manager
- Lifecycle policies enabled
- Enhanced monitoring and logging

## Monitoring and Debugging

### CloudWatch Logs
- Log group: `/aws/lambda/document-ingestion-lambda`
- Retention: 14 days (configurable)
- Structured logging with correlation IDs

### X-Ray Tracing
- Enabled for performance monitoring
- Traces Lambda execution and external API calls

### Metrics
- Lambda duration, memory usage, error rates
- S3 object counts and sizes
- Custom metrics from application code

## Security

### IAM Principle of Least Privilege
- Lambda execution role with minimal required permissions
- S3 access limited to specific bucket and prefixes
- Secrets Manager access limited to required secrets

### Secrets Management
- All sensitive data stored in AWS Secrets Manager
- No hardcoded credentials in code or configuration
- Automatic secret rotation support

### Network Security
- Lambda can be deployed in VPC if required
- S3 bucket with private ACL
- CORS configuration for web uploads

## Troubleshooting

### Common Issues
1. **Lambda timeout**: Increase timeout or memory allocation
2. **Permission denied**: Check IAM policies and secret ARNs
3. **Image not found**: Verify ECR repository and image URI
4. **S3 trigger not working**: Check bucket notification configuration

### Debug Commands
```bash
# Check Lambda function
aws lambda get-function --function-name function-name

# Check S3 bucket notifications
aws s3api get-bucket-notification-configuration --bucket bucket-name

# Check secrets
aws secretsmanager list-secrets

# View recent logs
aws logs tail /aws/lambda/function-name --follow
```

## Next Steps

1. Set up CI/CD pipeline for automated deployments
2. Implement monitoring dashboards
3. Add integration tests for different file formats
4. Implement batch processing for large files
5. Add support for additional file types beyond CSV
