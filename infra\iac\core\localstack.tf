# LocalStack Configuration for Document Ingestion Pipeline
# This file contains LocalStack-specific resources and configurations

# LocalStack Secrets Manager secrets for testing
resource "aws_secretsmanager_secret" "openai_api_key" {
  count = var.is_localstack ? 1 : 0
  
  name        = var.secrets_config.openai_api_key_secret_name
  description = "OpenAI API key for document processing (LocalStack)"
  
  tags = merge(local.this.tags, {
    Name        = "OpenAI API Key"
    Environment = "LocalStack"
    Service     = "Document Ingestion"
  })
}

resource "aws_secretsmanager_secret_version" "openai_api_key" {
  count = var.is_localstack ? 1 : 0
  
  secret_id     = aws_secretsmanager_secret.openai_api_key[0].id
  secret_string = "sk-test-localstack-openai-key-for-testing"
}

resource "aws_secretsmanager_secret" "supabase_url" {
  count = var.is_localstack ? 1 : 0
  
  name        = var.secrets_config.supabase_url_secret_name
  description = "Supabase URL for document processing (LocalStack)"
  
  tags = merge(local.this.tags, {
    Name        = "Supabase URL"
    Environment = "LocalStack"
    Service     = "Document Ingestion"
  })
}

resource "aws_secretsmanager_secret_version" "supabase_url" {
  count = var.is_localstack ? 1 : 0
  
  secret_id     = aws_secretsmanager_secret.supabase_url[0].id
  secret_string = "https://test-localstack.supabase.co"
}

resource "aws_secretsmanager_secret" "supabase_service_role_key" {
  count = var.is_localstack ? 1 : 0
  
  name        = var.secrets_config.supabase_service_role_key_secret_name
  description = "Supabase service role key for document processing (LocalStack)"
  
  tags = merge(local.this.tags, {
    Name        = "Supabase Service Role Key"
    Environment = "LocalStack"
    Service     = "Document Ingestion"
  })
}

resource "aws_secretsmanager_secret_version" "supabase_service_role_key" {
  count = var.is_localstack ? 1 : 0
  
  secret_id     = aws_secretsmanager_secret.supabase_service_role_key[0].id
  secret_string = "test-localstack-service-role-key"
}

resource "aws_secretsmanager_secret" "supabase_anon_key" {
  count = var.is_localstack ? 1 : 0
  
  name        = var.secrets_config.supabase_anon_key_secret_name
  description = "Supabase anonymous key for document processing (LocalStack)"
  
  tags = merge(local.this.tags, {
    Name        = "Supabase Anonymous Key"
    Environment = "LocalStack"
    Service     = "Document Ingestion"
  })
}

resource "aws_secretsmanager_secret_version" "supabase_anon_key" {
  count = var.is_localstack ? 1 : 0
  
  secret_id     = aws_secretsmanager_secret.supabase_anon_key[0].id
  secret_string = "test-localstack-anon-key"
}

# LocalStack ECR repository for document ingestion Lambda
resource "aws_ecr_repository" "document_ingestion" {
  count = var.is_localstack ? 1 : 0
  
  name                 = "lambda-document-ingestion"
  image_tag_mutability = "MUTABLE"
  
  image_scanning_configuration {
    scan_on_push = false  # Disable scanning in LocalStack
  }
  
  tags = merge(local.this.tags, {
    Name        = "Document Ingestion Lambda Repository"
    Environment = "LocalStack"
    Service     = "Document Ingestion"
  })
}

# Output LocalStack-specific information
output "localstack_resources" {
  description = "LocalStack-specific resource information"
  value = var.is_localstack ? {
    ecr_repository_url = aws_ecr_repository.document_ingestion[0].repository_url
    secrets = {
      openai_api_key_arn           = aws_secretsmanager_secret.openai_api_key[0].arn
      supabase_url_arn            = aws_secretsmanager_secret.supabase_url[0].arn
      supabase_service_role_key_arn = aws_secretsmanager_secret.supabase_service_role_key[0].arn
      supabase_anon_key_arn       = aws_secretsmanager_secret.supabase_anon_key[0].arn
    }
    endpoint = var.localstack_endpoint
  } : null
}
