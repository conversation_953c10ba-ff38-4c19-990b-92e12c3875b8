# Lambda Services Configuration
# This file defines all Lambda services deployed across environments.
# Environment-specific settings are handled via Terraform variables.

sample-lambda:
  # Container configuration
  image_repository_uri: ************.dkr.ecr.ap-southeast-5.amazonaws.com/lambda-nodejs-sample:latest

  # Environment variables (AWS_REGION is automatically set by Lambda)
  environment_variables:
    NODE_ENV: "production"
    LOG_LEVEL: "info"

  # API Gateway path patterns (like ECS) - simple and intuitive
  api_gateway:
    path_patterns:
      - "/sample"    # Exact match for /sample
      - "/sample/*"  # Matches /sample/* (any HTTP method)

  # Tags
  tags:
    Service: "Sample Lambda"
    Team: "Platform"
    ECRAccount: "************"

# Document Ingestion Pipeline - Clean Architecture Implementation
document-ingestion-lambda:
  # Container configuration - points to refactored clean architecture handler
  # Note: This will be dynamically replaced by Terraform locals based on environment
  image_repository_uri: "{{DOCUMENT_INGESTION_IMAGE_URI}}"

  # Lambda configuration optimized for document processing and embedding generation
  timeout: 900  # 15 minutes for large file processing and embedding generation
  memory_size: 2048  # 2GB for embedding generation and large CSV processing
  log_retention_days: 14  # 2 weeks retention for debugging
  publish: true  # Enable versioning for deployment tracking

  # Environment variables for clean architecture components
  environment_variables:
    # Application configuration
    LOG_LEVEL: "info"
    ENVIRONMENT: "{{ENVIRONMENT}}"
    SERVICE_VERSION: "1.0.0"

    # OpenAI API configuration
    OPENAI_API_KEY: "{{OPENAI_API_KEY_ARN}}"
    OPENAI_MODEL: "text-embedding-3-small"
    OPENAI_MAX_TOKENS: "8191"

    # Supabase configuration
    SUPABASE_URL: "{{SUPABASE_URL_ARN}}"
    SUPABASE_SERVICE_ROLE_KEY: "{{SUPABASE_SERVICE_ROLE_KEY_ARN}}"
    SUPABASE_ANON_KEY: "{{SUPABASE_ANON_KEY_ARN}}"

    # Processing configuration
    MAX_ROWS: "10000"
    RETRY_ATTEMPTS: "3"
    RETRY_MIN_WAIT: "1"
    RETRY_MAX_WAIT: "60"
    EMBEDDING_BATCH_SIZE: "100"
    SUPABASE_BATCH_SIZE: "50"
    SIMILARITY_THRESHOLD: "0.8"
    MAX_SEARCH_RESULTS: "20"

    # S3 configuration
    DOCUMENT_BUCKET_NAME: "{{DOCUMENT_BUCKET_NAME}}"
    AWS_REGION: "{{AWS_REGION}}"

  # Secrets from AWS Secrets Manager for secure credential management
  secrets:
    OPENAI_API_KEY: "{{OPENAI_API_KEY_ARN}}"
    SUPABASE_URL: "{{SUPABASE_URL_ARN}}"
    SUPABASE_SERVICE_ROLE_KEY: "{{SUPABASE_SERVICE_ROLE_KEY_ARN}}"
    SUPABASE_ANON_KEY: "{{SUPABASE_ANON_KEY_ARN}}"

  # Comprehensive IAM policy for document processing pipeline
  iam_policy: |
    {
      "Version": "2012-10-17",
      "Statement": [
        {
          "Sid": "S3DocumentBucketAccess",
          "Effect": "Allow",
          "Action": [
            "s3:GetObject",
            "s3:PutObject",
            "s3:DeleteObject",
            "s3:ListBucket",
            "s3:GetObjectVersion",
            "s3:PutObjectAcl"
          ],
          "Resource": [
            "arn:aws:s3:::{{DOCUMENT_BUCKET_NAME}}",
            "arn:aws:s3:::{{DOCUMENT_BUCKET_NAME}}/*"
          ]
        },
        {
          "Sid": "SecretsManagerAccess",
          "Effect": "Allow",
          "Action": [
            "secretsmanager:GetSecretValue",
            "secretsmanager:DescribeSecret"
          ],
          "Resource": [
            "{{OPENAI_API_KEY_ARN}}",
            "{{SUPABASE_URL_ARN}}",
            "{{SUPABASE_SERVICE_ROLE_KEY_ARN}}",
            "{{SUPABASE_ANON_KEY_ARN}}"
          ]
        },
        {
          "Sid": "CloudWatchLogsAccess",
          "Effect": "Allow",
          "Action": [
            "logs:CreateLogGroup",
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:DescribeLogGroups",
            "logs:DescribeLogStreams"
          ],
          "Resource": "arn:aws:logs:*:*:log-group:/aws/lambda/document-ingestion-lambda*"
        },
        {
          "Sid": "XRayTracingAccess",
          "Effect": "Allow",
          "Action": [
            "xray:PutTraceSegments",
            "xray:PutTelemetryRecords"
          ],
          "Resource": "*"
        }
      ]
    }

  # Tags for resource management and cost tracking
  tags:
    Service: "Document Ingestion Lambda"
    Team: "Platform"
    Architecture: "Clean Architecture"
    Handler: "src.presentation.lambda_handler.lambda_handler"
    ECRAccount: "************"
    Component: "Document Processing Pipeline"