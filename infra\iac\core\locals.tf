locals {
  this = {
    tags = merge({
      "environment" = var.env
    }, var.tags)
  }

  # Conditional domain naming
  # Production: api.{domain} (e.g., api.ezychat.ai)
  # Non-production: api.{env}.{domain} (e.g., api.uat.ezychat.ai, api.dev.ezychat.ai)
  api_domain_name = var.env == "prod" ? "${var.api_subdomain}.${var.domain_name}" : "${var.api_subdomain}.${var.env}.${var.domain_name}"

  app_domain_name = var.env == "prod" ? "${var.app_subdomain}.${var.domain_name}" : "${var.app_subdomain}.${var.env}.${var.domain_name}"

  # Wildcard certificate domain - covers all subdomains
  certificate_domain_name = "*.${var.domain_name}"

  # Document processing configuration with LocalStack overrides
  document_config = merge(var.document_processing_config, {
    # Override for LocalStack testing
    lambda_timeout = var.is_localstack ? 300 : var.document_processing_config.lambda_timeout
    lambda_memory_size = var.is_localstack ? 1024 : var.document_processing_config.lambda_memory_size
    log_retention_days = var.is_localstack ? 1 : var.document_processing_config.log_retention_days
  })

  # Secrets ARNs - conditional for LocalStack vs AWS
  secrets_arns = var.is_localstack ? {
    openai_api_key = "arn:aws:secretsmanager:us-east-1:000000000000:secret:${var.secrets_config.openai_api_key_secret_name}"
    supabase_url = "arn:aws:secretsmanager:us-east-1:000000000000:secret:${var.secrets_config.supabase_url_secret_name}"
    supabase_service_role_key = "arn:aws:secretsmanager:us-east-1:000000000000:secret:${var.secrets_config.supabase_service_role_key_secret_name}"
    supabase_anon_key = "arn:aws:secretsmanager:us-east-1:000000000000:secret:${var.secrets_config.supabase_anon_key_secret_name}"
  } : {
    openai_api_key = var.openai_api_key_secret_arn
    supabase_url = var.supabase_url_secret_arn
    supabase_service_role_key = var.supabase_service_role_key_secret_arn
    supabase_anon_key = var.supabase_anon_key_secret_arn
  }

  # ECR repository URI - conditional for LocalStack vs AWS
  document_ingestion_image_uri = var.is_localstack ? "${var.localstack_ecr_registry}/lambda-document-ingestion:latest" : "381491882604.dkr.ecr.ap-southeast-5.amazonaws.com/lambda-document-ingestion:latest"
}
